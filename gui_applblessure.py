# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import ttk, font, messagebox
import locale
import json
from datetime import datetime
import os

class BlessureApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تحليل جروح الطفولة - Analyse des blessures d'enfance")
        self.root.geometry("900x700")
        self.root.configure(bg="#f5f5f5")

        # Configurer pour supporter l'arabe (RTL)
        self.root.tk_setPalette(background='#f5f5f5')

        # Définir les blessures et leurs solutions (base de données complète)
        self.blessures = {
            "الخيانة/الاستغلال (CONTROLANT)": {
                "score": 0,
                "age": "2-6 سنوات",
                "description": "شعور بالخيانة أو الاستغلال في مرحلة الطفولة المبكرة. يتشكل عندما يشعر الطفل أن احتياجاته الأساسية للأمان والثقة قد تم انتهاكها.",
                "manifestations": [
                    "الميل للسيطرة على الآخرين والمواقف",
                    "صعوبة في الثقة بالآخرين",
                    "الحاجة المستمرة للتحكم في العلاقات",
                    "الشك والارتياب في نوايا الآخرين",
                    "الكمالية والحاجة لإثبات الذات"
                ],
                "exemples": [
                    "الوالدان يعدون بأشياء ولا يلتزمون بها",
                    "إجبار الطفل على تحمل مسؤوليات فوق سنه",
                    "استخدام الطفل كأداة في الصراعات الزوجية",
                    "التلاعب العاطفي والتهديد المستمر",
                    "عدم احترام خصوصية الطفل وحدوده الشخصية"
                ],
                "solutions": [
                    "العلاج السلوكي المعرفي (CBT) لتعديل الأفكار السلبية",
                    "تمارين الاسترخاء والتأمل للتخفيف من القلق والتوتر",
                    "بناء علاقات متوازنة تقوم على الاحترام المتبادل",
                    "تعلم مهارات التواصل الفعال والتعبير عن المشاعر",
                    "ممارسة التسامح مع الذات والآخرين",
                    "العمل مع معالج نفسي متخصص في صدمات الطفولة"
                ]
            },
            "الرفض/عدم القبول (FUYANT)": {
                "score": 0,
                "age": "منذ المرحلة الجنينية",
                "description": "شعور عميق بعدم القبول والرفض. يتشكل عندما يشعر الطفل أنه غير مرغوب فيه أو غير محبوب كما هو.",
                "manifestations": [
                    "الانسحاب من المواقف الاجتماعية",
                    "تجنب المواجهة والصراعات",
                    "صعوبة في التعبير عن الاحتياجات",
                    "الشعور بالنقص وعدم الاستحقاق",
                    "الميل للعزلة والوحدة"
                ],
                "exemples": [
                    "الإهمال العاطفي المبكر من قبل الوالدين",
                    "التعليقات السلبية المستمرة على وجود الطفل",
                    "مقارنة الطفل سلبياً بإخوته أو أقرانه",
                    "رفض الوالدين للطفل قبل الولادة أو عدم الرغبة في الحمل",
                    "عدم الاهتمام بإنجازات الطفل أو تقليل قيمتها"
                ],
                "solutions": [
                    "العلاج الجشطالتي لفك العزلة وبناء الثقة بالنفس",
                    "تمارين القبول الذاتي اليومية وتعزيز احترام الذات",
                    "الانضمام لمجموعات دعم اجتماعي",
                    "ممارسة التأكيدات الإيجابية والحديث الذاتي الإيجابي",
                    "العلاج بالفن للتعبير عن المشاعر المكبوتة",
                    "تعلم وضع حدود صحية في العلاقات"
                ]
            },
            "الإهمال/الهجر (DÉPENDANT)": {
                "score": 0,
                "age": "0-18 شهراً",
                "description": "شعور عميق بالهجر والوحدة. يتشكل عندما لا تُلبى احتياجات الطفل العاطفية والجسدية الأساسية.",
                "manifestations": [
                    "الخوف المستمر من الهجر في العلاقات",
                    "التعلق المفرط بالآخرين",
                    "صعوبة في الاستقلالية واتخاذ القرارات",
                    "الحاجة المستمرة للتأكيد والطمأنينة",
                    "الشعور بالفراغ الداخلي"
                ],
                "exemples": [
                    "غياب الوالدين المتكرر أو الطويل",
                    "عدم تلبية الاحتياجات العاطفية والجسدية الأساسية",
                    "الانفصال المبكر عن مقدم الرعاية الأساسي",
                    "التغييرات المتكررة في مقدمي الرعاية",
                    "إهمال بكاء الطفل أو احتياجاته الأساسية"
                ],
                "solutions": [
                    "العلاج المرتكز على التعلق لبناء نماذج تعلق آمنة",
                    "تعلم الاعتماد على الذات وتطوير الاستقلالية",
                    "بناء شبكة دعم اجتماعي متينة",
                    "ممارسة الوعي الذاتي والتأمل",
                    "تطوير هوايات واهتمامات شخصية",
                    "العمل مع معالج نفسي متخصص في قضايا التعلق"
                ]
            },
            "الإذلال/عدم الاحترام (MASOCHISTE)": {
                "score": 0,
                "age": "1-3 سنوات",
                "description": "شعور بالخجل والإذلال. يتشكل عندما يتعرض الطفل للإهانة أو الإذلال خلال مرحلة تطور هويته.",
                "manifestations": [
                    "الشعور بالخجل والعار من الذات",
                    "تقديم احتياجات الآخرين على احتياجات الذات",
                    "صعوبة في قبول المديح أو الاعتراف بالإنجازات",
                    "الميل للتضحية الذاتية المفرطة",
                    "الشعور بعدم الاستحقاق للسعادة"
                ],
                "exemples": [
                    "السخرية من الطفل أمام الآخرين",
                    "العقاب المهين أو المذل",
                    "انتقاد الطفل بشكل مستمر ومبالغ فيه",
                    "عدم احترام خصوصية الطفل أثناء تدريب استخدام الحمام",
                    "التعليقات السلبية على مظهر الطفل أو قدراته"
                ],
                "solutions": [
                    "العلاج المرتكز على الشفقة بالذات",
                    "تعلم وضع حدود صحية في العلاقات",
                    "ممارسة تقنيات تقدير الذات والثقة بالنفس",
                    "العلاج بالكتابة التعبيرية للتخلص من مشاعر العار",
                    "تطوير مهارات التواصل الحازم",
                    "العمل مع معالج نفسي متخصص في قضايا احترام الذات"
                ]
            },
            "عدم الأمان/الحرمان (SCHIZOÏDE)": {
                "score": 0,
                "age": "5-7 سنوات",
                "description": "شعور بعدم الأمان والحرمان العاطفي. يتشكل عندما لا يشعر الطفل بالأمان في بيئته أو عندما يحرم من التعبير عن احتياجاته.",
                "manifestations": [
                    "الانفصال عن المشاعر والعواطف",
                    "صعوبة في التواصل العاطفي مع الآخرين",
                    "الميل للعيش في عالم خيالي",
                    "الانسحاب من العلاقات الاجتماعية",
                    "الشعور بالاختلاف عن الآخرين"
                ],
                "exemples": [
                    "العيش في بيئة غير آمنة أو مضطربة",
                    "عدم السماح للطفل بالتعبير عن مشاعره",
                    "تجاهل احتياجات الطفل العاطفية",
                    "التعرض لصدمات أو أحداث مؤلمة دون دعم",
                    "النشأة في أسرة تفتقر للتواصل العاطفي"
                ],
                "solutions": [
                    "العلاج المرتكز على الصدمة",
                    "تعلم التواصل مع المشاعر وإدارتها",
                    "بناء علاقات آمنة وداعمة",
                    "ممارسة تقنيات التأريض للتعامل مع القلق",
                    "العلاج بالفن أو الموسيقى للتعبير عن المشاعر",
                    "العمل مع معالج نفسي متخصص في الصدمات النفسية"
                ]
            }
        }

        # Questions avec score associé et pondération
        self.questions = [
            {
                "text": "كيف تصف علاقاتك العاطفية؟",
                "options": {
                    "1": {"text": "أفضل أن أتحكم بكل التفاصيل", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 2}},
                    "2": {"text": "أتهرب منها عند الشعور بالألم", "scores": {"الرفض/عدم القبول (FUYANT)": 2}},
                    "3": {"text": "أحتاج إليها باستمرار وأخاف من فقدانها", "scores": {"الإهمال/الهجر (DÉPENDANT)": 2}},
                    "4": {"text": "أضع احتياجات الشريك قبل احتياجاتي دائماً", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2}},
                    "5": {"text": "أجد صعوبة في الانفتاح العاطفي والتعبير عن مشاعري", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 2}}
                }
            },
            {
                "text": "ما هو رد فعلك عند مواجهة مشكلة؟",
                "options": {
                    "1": {"text": "أصر على حلّها فوراً وأتولى زمام الأمور", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 2}},
                    "2": {"text": "أؤجلها أو أتجاهلها وأنسحب من المواجهة", "scores": {"الرفض/عدم القبول (FUYANT)": 2}},
                    "3": {"text": "أشعر بالقلق الشديد وأطلب المساعدة فوراً", "scores": {"الإهمال/الهجر (DÉPENDANT)": 2}},
                    "4": {"text": "أتحمل المسؤولية حتى لو لم تكن خطئي", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2}},
                    "5": {"text": "أنفصل عاطفياً وأتعامل مع المشكلة بشكل عقلاني بحت", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 2}}
                }
            },
            {
                "text": "كيف تتعامل مع النقد؟",
                "options": {
                    "1": {"text": "أدافع عن نفسي بقوة وأحاول إثبات أن النقد غير صحيح", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 1.5}},
                    "2": {"text": "أشعر بالرفض وأنسحب من الموقف", "scores": {"الرفض/عدم القبول (FUYANT)": 2}},
                    "3": {"text": "أشعر بالقلق من أن الشخص سيتركني بسبب أخطائي", "scores": {"الإهمال/الهجر (DÉPENDANT)": 1.5}},
                    "4": {"text": "أقبل النقد دائماً وأشعر بأنني أستحقه", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2}},
                    "5": {"text": "لا أظهر أي رد فعل عاطفي وأحلل النقد بشكل منطقي", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 1.5}}
                }
            },
            {
                "text": "ما هي ذكرياتك عن طفولتك؟",
                "options": {
                    "1": {"text": "كنت أشعر بأن علي تحمل مسؤوليات كبيرة في سن مبكرة", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 2}},
                    "2": {"text": "كنت أشعر بأنني غير مرغوب في أو غير محبوب كما أنا", "scores": {"الرفض/عدم القبول (FUYANT)": 2}},
                    "3": {"text": "كنت أشعر بالوحدة والخوف من الهجر", "scores": {"الإهمال/الهجر (DÉPENDANT)": 2}},
                    "4": {"text": "كنت أتعرض للإهانة أو السخرية أمام الآخرين", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2}},
                    "5": {"text": "كنت أعيش في بيئة غير آمنة أو مضطربة", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 2}}
                }
            },
            {
                "text": "كيف تتعامل مع الصراعات في العلاقات؟",
                "options": {
                    "1": {"text": "أحاول السيطرة على الموقف وفرض رأيي", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 1.5, "الإذلال/عدم الاحترام (MASOCHISTE)": -0.5}},
                    "2": {"text": "أتجنب الصراع وأنسحب من المواجهة", "scores": {"الرفض/عدم القبول (FUYANT)": 1.5, "عدم الأمان/الحرمان (SCHIZOÏDE)": 0.5}},
                    "3": {"text": "أخاف من أن يؤدي الصراع إلى هجر الطرف الآخر لي", "scores": {"الإهمال/الهجر (DÉPENDANT)": 2}},
                    "4": {"text": "أتنازل عن حقوقي لإرضاء الطرف الآخر", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2, "الإهمال/الهجر (DÉPENDANT)": 0.5}},
                    "5": {"text": "أنفصل عاطفياً وأتعامل مع الصراع بشكل عقلاني", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 1.5, "الرفض/عدم القبول (FUYANT)": 0.5}}
                }
            },
            {
                "text": "ما هي مخاوفك الأساسية في الحياة؟",
                "options": {
                    "1": {"text": "فقدان السيطرة أو التعرض للخداع", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 2}},
                    "2": {"text": "الرفض أو عدم القبول من الآخرين", "scores": {"الرفض/عدم القبول (FUYANT)": 2}},
                    "3": {"text": "الوحدة والهجر من الأحباء", "scores": {"الإهمال/الهجر (DÉPENDANT)": 2}},
                    "4": {"text": "عدم القدرة على إرضاء الآخرين", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2}},
                    "5": {"text": "التعرض للأذى العاطفي أو الصدمات", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 2}}
                }
            },
            {
                "text": "كيف ترى نفسك في العلاقات الاجتماعية؟",
                "options": {
                    "1": {"text": "قائد يتحمل المسؤولية ويتخذ القرارات", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 1.5}},
                    "2": {"text": "مستقل ويفضل العمل بمفرده", "scores": {"الرفض/عدم القبول (FUYANT)": 1.5, "عدم الأمان/الحرمان (SCHIZOÏDE)": 0.5}},
                    "3": {"text": "داعم للآخرين ويحتاج لدعمهم أيضاً", "scores": {"الإهمال/الهجر (DÉPENDANT)": 1.5}},
                    "4": {"text": "مضحي ويضع احتياجات الآخرين قبل احتياجاته", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2}},
                    "5": {"text": "مراقب يحلل الأمور من بعيد", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 1.5}}
                }
            },
            {
                "text": "ما هي علاقتك بوالديك في طفولتك؟",
                "options": {
                    "1": {"text": "كانوا يتوقعون مني الكثير أو يستغلون قدراتي", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 2}},
                    "2": {"text": "كانوا باردين عاطفياً أو يرفضونني", "scores": {"الرفض/عدم القبول (FUYANT)": 2}},
                    "3": {"text": "كانوا غائبين أو غير متوفرين عاطفياً", "scores": {"الإهمال/الهجر (DÉPENDANT)": 2}},
                    "4": {"text": "كانوا ينتقدونني باستمرار أو يهينونني", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2}},
                    "5": {"text": "كانوا غير قادرين على توفير بيئة آمنة", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 2}}
                }
            },
            {
                "text": "كيف تتعامل مع المشاعر القوية؟",
                "options": {
                    "1": {"text": "أحاول السيطرة عليها وعدم إظهارها", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 1.5, "عدم الأمان/الحرمان (SCHIZOÏDE)": 0.5}},
                    "2": {"text": "أنسحب وأتجنب المواقف التي تثيرها", "scores": {"الرفض/عدم القبول (FUYANT)": 1.5}},
                    "3": {"text": "أشعر بأنها تغمرني وأبحث عن دعم الآخرين", "scores": {"الإهمال/الهجر (DÉPENDANT)": 2}},
                    "4": {"text": "أكبتها وأضع مشاعر الآخرين قبل مشاعري", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 1.5}},
                    "5": {"text": "أنفصل عنها وأتعامل معها بشكل عقلاني", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 2}}
                }
            },
            {
                "text": "ما هي نظرتك للعالم بشكل عام؟",
                "options": {
                    "1": {"text": "مكان خطير يجب الحذر فيه من استغلال الآخرين", "scores": {"الخيانة/الاستغلال (CONTROLANT)": 1.5}},
                    "2": {"text": "مكان لا يقبلني كما أنا", "scores": {"الرفض/عدم القبول (FUYANT)": 2}},
                    "3": {"text": "مكان غير آمن يمكن أن يتركني فيه الأحباء", "scores": {"الإهمال/الهجر (DÉPENDANT)": 1.5}},
                    "4": {"text": "مكان يجب أن أثبت فيه قيمتي من خلال خدمة الآخرين", "scores": {"الإذلال/عدم الاحترام (MASOCHISTE)": 2}},
                    "5": {"text": "مكان مؤلم يجب الانفصال عنه عاطفياً", "scores": {"عدم الأمان/الحرمان (SCHIZOÏDE)": 1.5}}
                }
            }
        ]

        # Variables pour suivre l'état
        self.current_question = 0
        self.user_name = tk.StringVar()
        self.user_age = tk.StringVar()
        self.user_gender = tk.StringVar()
        self.user_responses = []

        # Variables pour la sélection multiple
        self.selected_options = {}  # Dictionnaire pour stocker les options sélectionnées pour chaque question

        # Créer les frames
        self.frame_welcome = tk.Frame(root, bg="#f5f5f5")
        self.frame_user_info = tk.Frame(root, bg="#f5f5f5")
        self.frame_questions = tk.Frame(root, bg="#f5f5f5")
        self.frame_results = tk.Frame(root, bg="#f5f5f5")

        # Initialiser l'interface
        self.setup_welcome_screen()
        self.setup_user_info_screen()
        self.setup_question_screen()
        self.setup_results_screen()

        # Créer un dossier pour les résultats s'il n'existe pas
        if not os.path.exists("results"):
            os.makedirs("results")

        # Afficher l'écran d'accueil
        self.show_welcome()

    def setup_welcome_screen(self):
        # Titre
        title_font = font.Font(family="Arial", size=24, weight="bold")
        title = tk.Label(self.frame_welcome, text="اختبار تحليل جروح الطفولة",
                         font=title_font, bg="#f5f5f5", fg="#333333")
        title.pack(pady=40)

        # Description
        desc_font = font.Font(family="Arial", size=12)
        description = tk.Label(self.frame_welcome,
                              text="هذا الاختبار يساعدك على فهم الجروح النفسية التي قد تكون تشكلت في طفولتك\n"
                                   "وكيفية التعامل معها للوصول إلى حياة نفسية أكثر توازناً\n\n"
                                   "يتضمن الاختبار 10 أسئلة تساعد في تحديد نوع الجرح النفسي الأساسي",
                              font=desc_font, bg="#f5f5f5", fg="#555555", justify=tk.RIGHT)
        description.pack(pady=20)

        # Informations sur la méthodologie
        methodology = tk.Label(self.frame_welcome,
                              text="يستند هذا الاختبار إلى نظرية الجروح النفسية للطفولة\n"
                                   "التي طورها المعالجون النفسيون المتخصصون في علاج صدمات الطفولة",
                              font=desc_font, bg="#f5f5f5", fg="#555555", justify=tk.RIGHT)
        methodology.pack(pady=10)

        # Bouton de démarrage
        start_button = tk.Button(self.frame_welcome, text="ابدأ الاختبار",
                                 command=self.show_user_info, bg="#4CAF50", fg="white",
                                 font=("Arial", 14), padx=20, pady=10)
        start_button.pack(pady=30)

    def setup_user_info_screen(self):
        # Titre
        title_font = font.Font(family="Arial", size=20, weight="bold")
        title = tk.Label(self.frame_user_info, text="معلومات المستخدم",
                        font=title_font, bg="#f5f5f5", fg="#333333")
        title.pack(pady=30)

        # Description
        desc_font = font.Font(family="Arial", size=12)
        description = tk.Label(self.frame_user_info,
                              text="يرجى إدخال بعض المعلومات الأساسية لتخصيص التحليل\n"
                                   "(هذه المعلومات اختيارية وتستخدم فقط لتحسين دقة التحليل)",
                              font=desc_font, bg="#f5f5f5", fg="#555555", justify=tk.RIGHT)
        description.pack(pady=20)

        # Formulaire
        form_frame = tk.Frame(self.frame_user_info, bg="#f5f5f5")
        form_frame.pack(pady=20, fill=tk.BOTH, padx=50)

        # Nom
        name_label = tk.Label(form_frame, text="الاسم:", font=("Arial", 12),
                             bg="#f5f5f5", fg="#333333", anchor=tk.E)
        name_label.grid(row=0, column=1, pady=10, sticky=tk.E)

        name_entry = tk.Entry(form_frame, textvariable=self.user_name,
                             font=("Arial", 12), width=30, justify=tk.RIGHT)
        name_entry.grid(row=0, column=0, pady=10, padx=10)

        # Âge
        age_label = tk.Label(form_frame, text="العمر:", font=("Arial", 12),
                            bg="#f5f5f5", fg="#333333", anchor=tk.E)
        age_label.grid(row=1, column=1, pady=10, sticky=tk.E)

        age_entry = tk.Entry(form_frame, textvariable=self.user_age,
                            font=("Arial", 12), width=30, justify=tk.RIGHT)
        age_entry.grid(row=1, column=0, pady=10, padx=10)

        # Genre
        gender_label = tk.Label(form_frame, text="الجنس:", font=("Arial", 12),
                               bg="#f5f5f5", fg="#333333", anchor=tk.E)
        gender_label.grid(row=2, column=1, pady=10, sticky=tk.E)

        gender_frame = tk.Frame(form_frame, bg="#f5f5f5")
        gender_frame.grid(row=2, column=0, pady=10, padx=10, sticky=tk.W)

        tk.Radiobutton(gender_frame, text="ذكر", variable=self.user_gender,
                      value="ذكر", font=("Arial", 12), bg="#f5f5f5").pack(side=tk.RIGHT, padx=10)
        tk.Radiobutton(gender_frame, text="أنثى", variable=self.user_gender,
                      value="أنثى", font=("Arial", 12), bg="#f5f5f5").pack(side=tk.RIGHT, padx=10)

        # Boutons
        button_frame = tk.Frame(self.frame_user_info, bg="#f5f5f5")
        button_frame.pack(pady=30)

        back_button = tk.Button(button_frame, text="رجوع",
                               command=self.show_welcome, bg="#f0f0f0", fg="#333333",
                               font=("Arial", 12), padx=15, pady=5)
        back_button.pack(side=tk.RIGHT, padx=10)

        next_button = tk.Button(button_frame, text="التالي",
                               command=self.start_test, bg="#2196F3", fg="white",
                               font=("Arial", 12), padx=15, pady=5)
        next_button.pack(side=tk.LEFT, padx=10)

    def setup_question_screen(self):
        # Conteneur principal
        main_container = tk.Frame(self.frame_questions, bg="#f5f5f5")
        main_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Barre de progression
        progress_frame = tk.Frame(main_container, bg="#f5f5f5")
        progress_frame.pack(fill=tk.X, pady=10)

        self.progress_label = tk.Label(progress_frame, text="السؤال 1 من 10",
                                      font=("Arial", 10), bg="#f5f5f5", fg="#777777")
        self.progress_label.pack(side=tk.RIGHT)

        self.progress_bar = ttk.Progressbar(main_container, orient="horizontal", length=100, mode="determinate")
        self.progress_bar.pack(fill=tk.X, pady=5)

        # Titre de la question
        self.question_title = tk.Label(main_container, text="",
                                      font=("Arial", 16, "bold"), bg="#f5f5f5", justify=tk.RIGHT)
        self.question_title.pack(pady=30, anchor=tk.E)

        # Frame pour les options
        self.options_frame = tk.Frame(main_container, bg="#f5f5f5")
        self.options_frame.pack(pady=20, fill=tk.BOTH, expand=True)

        # Boutons de navigation
        nav_frame = tk.Frame(main_container, bg="#f5f5f5")
        nav_frame.pack(pady=20, fill=tk.X)

        self.prev_button = tk.Button(nav_frame, text="السابق", command=self.prev_question,
                                    bg="#f0f0f0", fg="#333333", font=("Arial", 12), padx=15, pady=5)
        self.prev_button.pack(side=tk.LEFT, padx=20)

        self.next_button = tk.Button(nav_frame, text="التالي", command=self.next_question,
                                    bg="#2196F3", fg="white", font=("Arial", 12), padx=15, pady=5)
        self.next_button.pack(side=tk.RIGHT, padx=20)

    def setup_results_screen(self):
        # Titre
        result_title = tk.Label(self.frame_results, text="نتائج تحليل جروح الطفولة",
                               font=("Arial", 22, "bold"), bg="#f5f5f5", fg="#333333")
        result_title.pack(pady=30)

        # Conteneur pour les résultats
        self.result_container = tk.Frame(self.frame_results, bg="#f5f5f5")
        self.result_container.pack(pady=10, fill=tk.BOTH, expand=True, padx=30)

        # Boutons
        button_frame = tk.Frame(self.frame_results, bg="#f5f5f5")
        button_frame.pack(pady=20, fill=tk.X)

        save_button = tk.Button(button_frame, text="حفظ النتائج",
                               command=self.save_results, bg="#4CAF50", fg="white",
                               font=("Arial", 12), padx=15, pady=5)
        save_button.pack(side=tk.LEFT, padx=20)

        restart_button = tk.Button(button_frame, text="إعادة الاختبار",
                                  command=self.restart_test, bg="#FF5722", fg="white",
                                  font=("Arial", 12), padx=15, pady=5)
        restart_button.pack(side=tk.RIGHT, padx=20)

    def show_welcome(self):
        self.frame_results.pack_forget()
        self.frame_questions.pack_forget()
        self.frame_user_info.pack_forget()
        self.frame_welcome.pack(fill=tk.BOTH, expand=True)

    def show_user_info(self):
        self.frame_welcome.pack_forget()
        self.frame_results.pack_forget()
        self.frame_questions.pack_forget()
        self.frame_user_info.pack(fill=tk.BOTH, expand=True)

    def show_questions(self):
        self.frame_welcome.pack_forget()
        self.frame_user_info.pack_forget()
        self.frame_results.pack_forget()
        self.frame_questions.pack(fill=tk.BOTH, expand=True)
        self.display_current_question()

    def show_results(self):
        self.frame_welcome.pack_forget()
        self.frame_user_info.pack_forget()
        self.frame_questions.pack_forget()
        self.frame_results.pack(fill=tk.BOTH, expand=True)
        self.display_results()

    def start_test(self):
        # Réinitialiser les scores
        for blessure in self.blessures:
            self.blessures[blessure]["score"] = 0

        # Réinitialiser les réponses
        self.user_responses = []

        self.current_question = 0
        self.show_questions()

    def display_current_question(self):
        # Mettre à jour la barre de progression
        total_questions = len(self.questions)
        progress_percent = (self.current_question / total_questions) * 100
        self.progress_bar["value"] = progress_percent
        self.progress_label.config(text=f"السؤال {self.current_question + 1} من {total_questions}")

        # Gérer le bouton précédent
        if self.current_question == 0:
            self.prev_button.config(state=tk.DISABLED)
        else:
            self.prev_button.config(state=tk.NORMAL)

        # Effacer les options précédentes
        for widget in self.options_frame.winfo_children():
            widget.destroy()

        # Afficher la question actuelle
        q = self.questions[self.current_question]
        self.question_title.config(text=q["text"])

        # Initialiser les variables de sélection pour cette question si nécessaire
        if self.current_question not in self.selected_options:
            self.selected_options[self.current_question] = {}

        # Créer un cadre pour les options
        options_container = tk.Frame(self.options_frame, bg="#f5f5f5")
        options_container.pack(fill=tk.BOTH, expand=True, padx=10)

        # Ajouter une instruction pour la sélection multiple
        instruction = tk.Label(options_container,
                              text="يمكنك اختيار أكثر من إجابة إذا كانت تنطبق عليك",
                              font=("Arial", 10, "italic"), bg="#f5f5f5", fg="#666666",
                              justify=tk.RIGHT)
        instruction.pack(pady=(0, 10), anchor=tk.E)

        # Afficher les options avec des cases à cocher
        for key, option in q["options"].items():
            option_text = option["text"]

            # Créer un cadre pour chaque option
            option_frame = tk.Frame(options_container, bg="#f5f5f5", bd=1, relief=tk.SOLID)
            option_frame.pack(pady=5, fill=tk.X, ipady=5)

            # Variable pour la case à cocher
            var = tk.BooleanVar()
            var.set(False)  # Par défaut, non cochée

            # Restaurer l'état précédent si disponible
            if key in self.selected_options[self.current_question]:
                var.set(self.selected_options[self.current_question][key])

            # Stocker la variable dans le dictionnaire
            self.selected_options[self.current_question][key] = var

            # Créer la case à cocher
            cb = tk.Checkbutton(option_frame, text=option_text, variable=var,
                               font=("Arial", 12), bg="#f5f5f5", anchor=tk.W,
                               justify=tk.RIGHT, wraplength=700,
                               command=self.update_next_button)
            cb.pack(side=tk.TOP, anchor=tk.E, padx=10, pady=5, fill=tk.X)

            # Ajouter une explication si disponible
            if "explanation" in option:
                explanation = tk.Label(option_frame, text=option["explanation"],
                                      font=("Arial", 10, "italic"), bg="#f5f5f5", fg="#666666",
                                      justify=tk.RIGHT, wraplength=700)
                explanation.pack(side=tk.TOP, anchor=tk.E, padx=30, pady=(0, 5), fill=tk.X)

        # Mettre à jour l'état du bouton suivant
        self.update_next_button()

    def update_next_button(self):
        """Active le bouton suivant si au moins une option est sélectionnée"""
        # Vérifier si au moins une option est sélectionnée
        any_selected = False

        if self.current_question in self.selected_options:
            for key, var in self.selected_options[self.current_question].items():
                if var.get():
                    any_selected = True
                    break

        # Activer ou désactiver le bouton suivant
        if any_selected:
            self.next_button.config(state=tk.NORMAL)
        else:
            self.next_button.config(state=tk.DISABLED)

    def prev_question(self):
        # Revenir à la question précédente
        self.current_question -= 1
        self.display_current_question()

    def next_question(self):
        # Mettre à jour les scores en fonction des options sélectionnées
        q = self.questions[self.current_question]

        # Réinitialiser les scores pour cette question (pour éviter de compter deux fois)
        for blessure in self.blessures:
            # Créer un attribut pour suivre les scores par question si nécessaire
            if "scores_by_question" not in self.blessures[blessure]:
                self.blessures[blessure]["scores_by_question"] = {}

            # Soustraire le score précédent pour cette question s'il existe
            if self.current_question in self.blessures[blessure]["scores_by_question"]:
                self.blessures[blessure]["score"] -= self.blessures[blessure]["scores_by_question"][self.current_question]
                self.blessures[blessure]["scores_by_question"][self.current_question] = 0

        # Appliquer les scores pour chaque option sélectionnée
        for key, var in self.selected_options[self.current_question].items():
            if var.get():  # Si l'option est sélectionnée
                option = q["options"][key]

                # Appliquer les scores pour chaque blessure mentionnée
                for blessure, score in option["scores"].items():
                    self.blessures[blessure]["score"] += score

                    # Enregistrer le score pour cette question
                    if self.current_question not in self.blessures[blessure]["scores_by_question"]:
                        self.blessures[blessure]["scores_by_question"][self.current_question] = 0

                    self.blessures[blessure]["scores_by_question"][self.current_question] += score

        # Passer à la question suivante ou afficher les résultats
        self.current_question += 1
        if self.current_question < len(self.questions):
            self.display_current_question()
        else:
            self.show_results()

    def display_results(self):
        # Effacer les résultats précédents
        for widget in self.result_container.winfo_children():
            widget.destroy()

        # Créer un canvas avec scrollbar pour les résultats détaillés
        canvas_frame = tk.Frame(self.result_container)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        canvas = tk.Canvas(canvas_frame, bg="#f5f5f5")
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f5f5")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Calculer les scores finaux (en pourcentage)
        max_possible_score = 0
        for q in self.questions:
            # Calculer le score maximum possible par question
            question_max = 0
            for option in q["options"].values():
                option_max = sum(score for score in option["scores"].values() if score > 0)
                question_max = max(question_max, option_max)
            max_possible_score += question_max

        score_percentages = {}
        for blessure, info in self.blessures.items():
            score_percentages[blessure] = (info["score"] / max_possible_score) * 100 if max_possible_score > 0 else 0

        # Trier les blessures par score décroissant
        sorted_blessures = sorted(score_percentages.items(), key=lambda x: x[1], reverse=True)

        # Afficher un résumé des scores
        summary_frame = tk.Frame(scrollable_frame, bg="#ffffff", bd=1, relief=tk.SOLID)
        summary_frame.pack(pady=10, padx=10, fill=tk.X)

        tk.Label(summary_frame, text="ملخص النتائج",
                font=("Arial", 16, "bold"), bg="#ffffff", fg="#333333").pack(pady=10)

        # Créer un graphique simple des scores
        graph_frame = tk.Frame(summary_frame, bg="#ffffff")
        graph_frame.pack(pady=10, fill=tk.X, padx=20)

        for blessure, percentage in sorted_blessures:
            # Nom de la blessure
            name_label = tk.Label(graph_frame, text=blessure,
                                 font=("Arial", 11), bg="#ffffff", fg="#333333", width=25, anchor=tk.E)
            name_label.pack(anchor=tk.E, pady=2)

            # Barre de pourcentage
            bar_frame = tk.Frame(graph_frame, bg="#ffffff")
            bar_frame.pack(fill=tk.X, pady=2)

            bar_width = int(percentage * 5)  # Échelle pour l'affichage
            bar = tk.Frame(bar_frame, bg="#4CAF50", height=15, width=bar_width)
            bar.pack(side=tk.LEFT)

            # Pourcentage
            percent_label = tk.Label(bar_frame, text=f"{percentage:.1f}%",
                                    font=("Arial", 10), bg="#ffffff", fg="#555555")
            percent_label.pack(side=tk.LEFT, padx=10)

        # Afficher les détails de la blessure principale
        main_blessure = sorted_blessures[0][0]
        info = self.blessures[main_blessure]

        # Cadre pour la blessure principale
        main_frame = tk.Frame(scrollable_frame, bg="#ffffff", bd=1, relief=tk.SOLID)
        main_frame.pack(pady=20, padx=10, fill=tk.X)

        # Titre
        tk.Label(main_frame, text=f"الجرح الرئيسي: {main_blessure}",
                font=("Arial", 16, "bold"), bg="#ffffff", fg="#333333").pack(pady=10, anchor=tk.E, padx=15)

        # Âge de formation
        if "age" in info:
            tk.Label(main_frame, text=f"يتشكل في: {info['age']}",
                    font=("Arial", 12), bg="#ffffff", fg="#555555").pack(pady=5, anchor=tk.E, padx=15)

        # Description
        tk.Label(main_frame, text=f"الوصف: {info['description']}",
                font=("Arial", 12), bg="#ffffff", fg="#555555", justify=tk.RIGHT, wraplength=800).pack(pady=10, anchor=tk.E, padx=15)

        # Manifestations
        if "manifestations" in info:
            tk.Label(main_frame, text="كيف يظهر في حياتك اليوم:",
                    font=("Arial", 14, "bold"), bg="#ffffff", fg="#333333").pack(pady=(15, 5), anchor=tk.E, padx=15)

            for m in info["manifestations"]:
                tk.Label(main_frame, text=f"• {m}",
                        font=("Arial", 11), bg="#ffffff", fg="#555555", justify=tk.RIGHT, wraplength=750).pack(pady=3, anchor=tk.E, padx=25)

        # Exemples
        tk.Label(main_frame, text="أمثلة من الطفولة:",
                font=("Arial", 14, "bold"), bg="#ffffff", fg="#333333").pack(pady=(15, 5), anchor=tk.E, padx=15)

        for ex in info["exemples"]:
            tk.Label(main_frame, text=f"• {ex}",
                    font=("Arial", 11), bg="#ffffff", fg="#555555", justify=tk.RIGHT, wraplength=750).pack(pady=3, anchor=tk.E, padx=25)

        # Solutions
        tk.Label(main_frame, text="اقتراحات للشفاء:",
                font=("Arial", 14, "bold"), bg="#ffffff", fg="#333333").pack(pady=(15, 5), anchor=tk.E, padx=15)

        for sol in info["solutions"]:
            tk.Label(main_frame, text=f"• {sol}",
                    font=("Arial", 11), bg="#ffffff", fg="#555555", justify=tk.RIGHT, wraplength=750).pack(pady=3, anchor=tk.E, padx=25)

        # Analyse des choix sélectionnés
        analysis_frame = tk.Frame(scrollable_frame, bg="#ffffff", bd=1, relief=tk.SOLID)
        analysis_frame.pack(pady=20, padx=10, fill=tk.X)

        tk.Label(analysis_frame, text="تحليل مفصل لإجاباتك:",
                font=("Arial", 16, "bold"), bg="#ffffff", fg="#333333").pack(pady=10, anchor=tk.E, padx=15)

        # Analyser les réponses pour chaque question
        for q_idx, question in enumerate(self.questions):
            if q_idx in self.selected_options:
                selected_keys = [key for key, var in self.selected_options[q_idx].items() if var.get()]

                if selected_keys:
                    # Créer un cadre pour chaque question
                    q_frame = tk.Frame(analysis_frame, bg="#ffffff", bd=1, relief=tk.GROOVE)
                    q_frame.pack(pady=10, padx=15, fill=tk.X)

                    # Afficher la question
                    tk.Label(q_frame, text=f"السؤال {q_idx + 1}: {question['text']}",
                            font=("Arial", 12, "bold"), bg="#ffffff", fg="#333333",
                            justify=tk.RIGHT, wraplength=750).pack(pady=5, anchor=tk.E, padx=10)

                    # Afficher les options sélectionnées
                    tk.Label(q_frame, text="إجاباتك:",
                            font=("Arial", 11, "bold"), bg="#ffffff", fg="#555555").pack(pady=5, anchor=tk.E, padx=10)

                    # Collecter les blessures affectées par cette question
                    affected_blessures = {}

                    for key in selected_keys:
                        option = question["options"][key]

                        # Afficher l'option sélectionnée
                        tk.Label(q_frame, text=f"• {option['text']}",
                                font=("Arial", 10), bg="#ffffff", fg="#555555",
                                justify=tk.RIGHT, wraplength=700).pack(pady=2, anchor=tk.E, padx=20)

                        # Collecter les blessures affectées
                        for blessure, score in option["scores"].items():
                            if blessure not in affected_blessures:
                                affected_blessures[blessure] = 0
                            affected_blessures[blessure] += score

                    # Afficher l'analyse pour cette question
                    if affected_blessures:
                        tk.Label(q_frame, text="تحليل:",
                                font=("Arial", 11, "bold"), bg="#ffffff", fg="#555555").pack(pady=5, anchor=tk.E, padx=10)

                        # Trier les blessures par score décroissant
                        sorted_affected = sorted(affected_blessures.items(), key=lambda x: x[1], reverse=True)

                        for blessure, score in sorted_affected:
                            if score > 0:
                                # Obtenir des informations sur cette blessure
                                blessure_info = self.blessures[blessure]

                                # Créer une analyse personnalisée
                                analysis_text = self.generate_analysis_for_question(q_idx, blessure, score)

                                tk.Label(q_frame, text=f"• {analysis_text}",
                                        font=("Arial", 10), bg="#ffffff", fg="#555555",
                                        justify=tk.RIGHT, wraplength=700).pack(pady=2, anchor=tk.E, padx=20)

        # Analyse des combinaisons de blessures
        if len(sorted_blessures) > 1 and sorted_blessures[1][1] > 30:  # Si la deuxième blessure a un score significatif
            combination_frame = tk.Frame(scrollable_frame, bg="#ffffff", bd=1, relief=tk.SOLID)
            combination_frame.pack(pady=20, padx=10, fill=tk.X)

            tk.Label(combination_frame, text="تحليل تفاعل الجروح:",
                    font=("Arial", 16, "bold"), bg="#ffffff", fg="#333333").pack(pady=10, anchor=tk.E, padx=15)

            # Analyser l'interaction entre les deux principales blessures
            primary_wound = sorted_blessures[0][0]
            secondary_wound = sorted_blessures[1][0]

            interaction_text = self.analyze_wound_interaction(primary_wound, secondary_wound)

            tk.Label(combination_frame, text=interaction_text,
                    font=("Arial", 12), bg="#ffffff", fg="#555555",
                    justify=tk.RIGHT, wraplength=800).pack(pady=10, anchor=tk.E, padx=15)

    def generate_analysis_for_question(self, question_idx, blessure, score):
        """Génère une analyse personnalisée pour une question et une blessure spécifiques"""
        question = self.questions[question_idx]

        # Textes d'analyse par défaut pour chaque blessure
        default_analyses = {
            "الخيانة/الاستغلال (CONTROLANT)": "تظهر إجابتك ميلاً للسيطرة والتحكم، وهو ما يرتبط بجرح الخيانة/الاستغلال",
            "الرفض/عدم القبول (FUYANT)": "تعكس إجابتك مشاعر الرفض وعدم القبول التي قد تكون تشكلت في طفولتك",
            "الإهمال/الهجر (DÉPENDANT)": "تشير إجابتك إلى خوف من الهجر والحاجة للتعلق بالآخرين",
            "الإذلال/عدم الاحترام (MASOCHISTE)": "تظهر إجابتك نمطاً من التضحية بالذات وتقديم احتياجات الآخرين على احتياجاتك",
            "عدم الأمان/الحرمان (SCHIZOÏDE)": "تعكس إجابتك ميلاً للانفصال العاطفي والانسحاب من المواقف المؤلمة"
        }

        # Analyses spécifiques pour certaines questions et blessures
        specific_analyses = {
            0: {  # Question sur les relations amoureuses
                "الخيانة/الاستغلال (CONTROLANT)": "تظهر حاجتك للسيطرة في العلاقات العاطفية، وهي طريقة للتعامل مع الخوف من الخيانة",
                "الرفض/عدم القبول (FUYANT)": "ميلك للهروب من العلاقات عند الشعور بالألم يعكس خوفك العميق من الرفض",
                "الإهمال/الهجر (DÉPENDANT)": "حاجتك المستمرة للعلاقات تعكس خوفك من الهجر الذي تشكل في طفولتك المبكرة"
            },
            1: {  # Question sur la résolution de problèmes
                "الخيانة/الاستغلال (CONTROLANT)": "إصرارك على حل المشاكل فوراً يعكس حاجتك للسيطرة على المواقف",
                "الرفض/عدم القبول (FUYANT)": "تجنبك للمشاكل هو آلية دفاعية ضد مشاعر الرفض المحتملة"
            }
            # Ajouter d'autres analyses spécifiques selon les questions
        }

        # Choisir l'analyse appropriée
        if question_idx in specific_analyses and blessure in specific_analyses[question_idx]:
            return specific_analyses[question_idx][blessure]
        else:
            return default_analyses.get(blessure, "تعكس إجابتك جوانب مهمة من شخصيتك تتعلق بتجارب طفولتك")

    def analyze_wound_interaction(self, primary_wound, secondary_wound):
        """Analyse l'interaction entre deux blessures principales"""

        # Définir les analyses d'interaction pour différentes combinaisons de blessures
        interactions = {
            ("الخيانة/الاستغلال (CONTROLANT)", "الرفض/عدم القبول (FUYANT)"):
                "الجمع بين جرح الخيانة وجرح الرفض يمكن أن يخلق صراعاً داخلياً بين الرغبة في السيطرة والميل للانسحاب. "
                "قد تجد نفسك تتحكم في بعض المواقف بينما تنسحب تماماً من مواقف أخرى. "
                "العلاج يتطلب تطوير توازن صحي بين الاستقلالية والانفتاح على الآخرين.",

            ("الخيانة/الاستغلال (CONTROLANT)", "الإهمال/الهجر (DÉPENDANT)"):
                "الجمع بين جرح الخيانة وجرح الإهمال يخلق تناقضاً بين الحاجة للسيطرة والخوف من الهجر. "
                "قد تجد نفسك تتحكم في العلاقات خوفاً من فقدانها، مما يخلق دورة سلبية. "
                "العلاج يتطلب بناء الثقة بالنفس والآخرين وتعلم التوازن في العلاقات.",

            ("الرفض/عدم القبول (FUYANT)", "الإهمال/الهجر (DÉPENDANT)"):
                "الجمع بين جرح الرفض وجرح الإهمال يخلق صراعاً بين الرغبة في الانسحاب والحاجة للتعلق. "
                "قد تجد نفسك في علاقات متناقضة حيث تحتاج للقرب ولكنك تخاف من الرفض. "
                "العلاج يتطلب تطوير قبول الذات والتعلق الآمن.",

            ("الإذلال/عدم الاحترام (MASOCHISTE)", "الإهمال/الهجر (DÉPENDANT)"):
                "الجمع بين جرح الإذلال وجرح الإهمال يمكن أن يؤدي إلى نمط من التضحية المفرطة بالذات للحفاظ على العلاقات. "
                "قد تجد نفسك تقبل بأي معاملة خوفاً من الهجر. "
                "العلاج يتطلب تعلم وضع حدود صحية وتطوير احترام الذات."
        }

        # Vérifier si la combinaison existe dans notre dictionnaire
        key = (primary_wound, secondary_wound)
        reverse_key = (secondary_wound, primary_wound)

        if key in interactions:
            return interactions[key]
        elif reverse_key in interactions:
            return interactions[reverse_key]
        else:
            # Analyse générique si la combinaison spécifique n'est pas définie
            return (f"التفاعل بين {primary_wound} و{secondary_wound} يخلق ديناميكية فريدة في شخصيتك. "
                   f"هذه الجروح تتفاعل وتؤثر على طريقة تعاملك مع العلاقات والمواقف الحياتية. "
                   f"العلاج يتطلب فهم كيفية تأثير كل جرح على الآخر والعمل على شفاء كليهما بشكل متوازن.")

    def save_results(self):
        """Sauvegarder les résultats dans un fichier JSON"""
        try:
            # Calculer les scores finaux
            max_possible_score = sum(2 for _ in self.questions)
            score_percentages = {}

            for blessure, info in self.blessures.items():
                score_percentages[blessure] = (info["score"] / max_possible_score) * 100

            # Préparer les données
            results = {
                "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "user_info": {
                    "name": self.user_name.get(),
                    "age": self.user_age.get(),
                    "gender": self.user_gender.get()
                },
                "scores": score_percentages,
                "responses": self.user_responses,
                "main_wound": max(score_percentages.items(), key=lambda x: x[1])[0]
            }

            # Créer un nom de fichier
            filename = f"results/result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            # Sauvegarder le fichier
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=4)

            messagebox.showinfo("تم الحفظ", f"تم حفظ النتائج بنجاح في الملف:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتائج:\n{str(e)}")

    def restart_test(self):
        # Réinitialiser les variables
        self.user_responses = []
        self.current_question = 0

        # Retourner à l'écran d'accueil
        self.show_welcome()

def main():
    root = tk.Tk()
    app = BlessureApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
