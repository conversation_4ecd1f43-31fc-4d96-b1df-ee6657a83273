@echo off
echo Création de l'exécutable pour l'application d'analyse des blessures d'enfance...
echo.

REM Vérifier si PyInstaller est installé
pip show pyinstaller > nul 2>&1
if %errorlevel% neq 0 (
    echo PyInstaller n'est pas installé. Installation en cours...
    pip install pyinstaller
) else (
    echo PyInstaller est déjà installé.
)

echo.
echo Création de l'exécutable...
echo.

REM Créer l'exécutable
pyinstaller --onefile --windowed --name "AnalyseBlessuresEnfance" gui_applblessure.py

echo.
if %errorlevel% equ 0 (
    echo L'exécutable a été créé avec succès !
    echo Vous pouvez le trouver dans le dossier "dist".
    echo.
    echo Chemin complet : %cd%\dist\AnalyseBlessuresEnfance.exe
) else (
    echo Une erreur s'est produite lors de la création de l'exécutable.
)

echo.
echo Appuyez sur une touche pour quitter...
pause > nul
