# -*- coding: utf-8 -*-

def main():
    # Définir les blessures et leurs solutions
    blessures = {
        "الخيانة/الاستغلال (CONTROLANT)": {
            "score": 0,
            "description": "شعور بالخيانة أو الاستغلال بين 2-6 سنوات",
            "exemples": [
                "الوالدان يعدون بأشياء ولا يلتزمون",
                "إجبار الطفل على تحمل مسؤوليات فوق سنه"
            ],
            "solutions": [
                "بناء الثقة عبر علاقات متوازنة",
                "جلسات العلاج السلوكي المعرفي",
                "ممارسة تقنيات التأمل للتحكم بالقلق"
            ]
        },
        "الرفض/عدم القبول (FUYANT)": {
            "score": 0,
            "description": "شعور بعدم القبول منذ المرحلة الجنينية",
            "exemples": [
                "الإهمال العاطفي المبكر",
                "التعليقات السلبية على وجود الطفل"
            ],
            "solutions": [
                "تمارين القبول الذاتي اليومية",
                "العلاج الجشطالتي لفك العزلة",
                "الانضمام لمجموعات دعم اجتماعي"
            ]
        },
        # Ajoutez les autres blessures de la même manière...
    }

    # Questions avec score associé
    questions = [
        {
            "text": "كيف تصف علاقاتك العاطفية؟",
            "options": {
                "1": "أفضل أن أتحكم بكل التفاصيل (نقطة للخيانة)",
                "2": "أتهرب منها عند الشعور بالألم (نقطة للرفض)",
                "3": "أحتاج إليها باستمرار (نقطة للإهمال)"
            }
        },
        {
            "text": "رد فعلك عند مواجهة مشكلة؟",
            "options": {
                "1": "أصر على حلّها فورًا (خيانة)",
                "2": "أؤجلها أو أتجاهلها (رفض)",
                "3": "أطلب المساعدة مباشرة (إهمال)"
            }
        }
    ]

    # Poser les questions
    print("\n===== اختبار تحليل جروح الطفولة =====\n")
    for q in questions:
        print(q["text"])
        for key, value in q["options"].items():
            print(f"{key}. {value}")
        
        while True:
            choix = input("\nاختر رقم الإجابة: ")
            if choix in q["options"]:
                # Mettre à jour les scores
                if "خيانة" in q["options"][choix]:
                    blessures["الخيانة/الاستغلال (CONTROLANT)"]["score"] += 1
                elif "رفض" in q["options"][choix]:
                    blessures["الرفض/عدم القبول (FUYANT)"]["score"] += 1
                break
            else:
                print("اختيار غير صحيح!")

    # Déterminer النتيجة
    max_score = max(blessures.values(), key=lambda x: x["score"])
    resultat = [name for name, info in blessures.items() if info["score"] == max_score["score"]]

    # عرض النتيجة
    print("\n===== النتائج =====")
    for blessure in resultat:
        info = blessures[blessure]
        print(f"\nالجرح: {blessure}")
        print(f"الوصف: {info['description']}")
        
        print("\nأمثلة من الطفولة:")
        for ex in info["exemples"]:
            print(f"- {ex}")
        
        print("\nاقتراحات للشفاء:")
        for sol in info["solutions"]:
            print(f"• {sol}")

if __name__ == "__main__":
    main()