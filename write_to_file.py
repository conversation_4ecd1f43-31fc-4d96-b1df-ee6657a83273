# -*- coding: utf-8 -*-

def main():
    # Ouvrir un fichier en mode écriture avec encodage UTF-8
    with open("output.txt", "w", encoding="utf-8") as f:
        # Écrire du texte arabe dans le fichier
        f.write("Test d'affichage de caractères arabes:\n")
        f.write("اختبار تحليل جروح الطفولة\n")
        f.write("الخيانة/الاستغلال\n")
        f.write("الرفض/عدم القبول\n")
        
        # Informations sur le test
        f.write("\nCe fichier a été créé pour tester l'affichage des caractères arabes.\n")
        f.write("Si vous pouvez lire ce texte correctement, l'encodage UTF-8 fonctionne bien.\n")
    
    print("Le fichier output.txt a été créé avec succès.")
    print("Veuillez l'ouvrir pour vérifier l'affichage des caractères arabes.")

if __name__ == "__main__":
    main()
