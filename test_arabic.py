# -*- coding: utf-8 -*-
import sys
import io

# Configurer l'encodage de sortie pour la console
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

def main():
    # Test d'affichage de caractères arabes
    print("Test d'affichage de caractères arabes:")
    print("اختبار تحليل جروح الطفولة")
    print("الخيانة/الاستغلال")
    print("الرفض/عدم القبول")
    
    # Afficher l'encodage utilisé
    print(f"\nEncodage de la console: {sys.stdout.encoding}")
    print(f"Encodage par défaut: {sys.getdefaultencoding()}")
    
    # Attendre que l'utilisateur appuie sur Entrée pour quitter
    input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()
