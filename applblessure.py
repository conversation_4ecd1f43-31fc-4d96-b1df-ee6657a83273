# -*- coding: utf-8 -*-

def main():
    # Ouvrir un fichier pour écrire les résultats
    output_file = open("resultats_blessures.txt", "w", encoding="utf-8")
     # Définir les blessures et leurs solutions (base de données complète)
    # Définir les blessures et leurs solutions
    blessures = {
        "الخيانة/الاستغلال (CONTROLANT)": {
            "score": 0,
            "description": "شعور بالخيانة أو الاستغلال بين 2-6 سنوات",
            "exemples": [
                "الوالدان يعدون بأشياء ولا يلتزمون",
                "إجبار الطفل على تحمل مسؤوليات فوق سنه"
            ],
            "solutions": [
                "بناء الثقة عبر علاقات متوازنة",
                "جلسات العلاج السلوكي المعرفي",
                "ممارسة تقنيات التأمل للتحكم بالقلق"
            ]
        },
        "الرفض/عدم القبول (FUYANT)": {
            "score": 0,
            "description": "شعور بعدم القبول منذ المرحلة الجنينية",
            "exemples": [
                "الإهمال العاطفي المبكر",
                "التعليقات السلبية على وجود الطفل"
            ],
            "solutions": [
                "تمارين القبول الذاتي اليومية",
                "العلاج الجشطالتي لفك العزلة",
                "الانضمام لمجموعات دعم اجتماعي"
            ]
        },
        # Ajoutez les autres blessures de la même manière...
    }

    # Questions avec score associé
    questions = [
        {
            "text": "كيف تصف علاقاتك العاطفية؟",
            "options": {
                "1": "أفضل أن أتحكم بكل التفاصيل (نقطة للخيانة)",
                "2": "أتهرب منها عند الشعور بالألم (نقطة للرفض)",
                "3": "أحتاج إليها باستمرار (نقطة للإهمال)"
            }
        },
        {
            "text": "رد فعلك عند مواجهة مشكلة؟",
            "options": {
                "1": "أصر على حلّها فورًا (خيانة)",
                "2": "أؤجلها أو أتجاهلها (رفض)",
                "3": "أطلب المساعدة مباشرة (إهمال)"
            }
        }
    ]

    # Écrire le titre du test dans le fichier
    output_file.write("\n===== اختبار تحليل جروح الطفولة =====\n\n")

    # Simuler des réponses pour tester
    output_file.write("Test d'affichage des caractères arabes\n")
    output_file.write("Simulation de réponses automatiques\n\n")

    # Mettre à jour les scores avec des valeurs de test
    blessures["الخيانة/الاستغلال (CONTROLANT)"]["score"] = 1
    blessures["الرفض/عدم القبول (FUYANT)"]["score"] = 1

    # Déterminer النتيجة
    max_score = max(blessures.values(), key=lambda x: x["score"])
    resultat = [name for name, info in blessures.items() if info["score"] == max_score["score"]]

    # عرض النتيجة
    output_file.write("\n===== النتائج =====\n")
    for blessure in resultat:
        info = blessures[blessure]
        output_file.write(f"\nالجرح: {blessure}\n")
        output_file.write(f"الوصف: {info['description']}\n")

        output_file.write("\nأمثلة من الطفولة:\n")
        for ex in info["exemples"]:
            output_file.write(f"- {ex}\n")

        output_file.write("\nاقتراحات للشفاء:\n")
        for sol in info["solutions"]:
            output_file.write(f"• {sol}\n")

    # Fermer le fichier
    output_file.close()

    # Afficher un message de confirmation
    print("Les résultats ont été écrits dans le fichier 'resultats_blessures.txt'")
    print("Veuillez ouvrir ce fichier pour voir les résultats en arabe.")

if __name__ == "__main__":
    main()